"""
数据可视化期末考核 - 完整代码
作者：学生姓名
日期：2024年
描述：基于Python的多维度数据可视化分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# ==================== 基础配置 ====================
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# 创建输出目录
import os
if not os.path.exists('charts'):
    os.makedirs('charts')

print("开始生成数据可视化图表...")
print("="*60)

# ==================== 数据集1: 营销和产品销售表 ====================
print("\n1. 营销和产品销售表 - 生成3个图表")
marketing_df = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
marketing_df['日期'] = pd.to_datetime(marketing_df['日期'])

# 图表1: 折线图 - 营销费用与订单金额时间趋势
def create_marketing_trend_chart():
    """创建营销趋势折线图"""
    plt.figure(figsize=(12, 6))
    plt.plot(marketing_df['日期'], marketing_df['营销费用（元）'],
             marker='o', linewidth=2, label='营销费用', color='#FF6B6B')
    plt.plot(marketing_df['日期'], marketing_df['订单金额（元）'],
             marker='s', linewidth=2, label='订单金额', color='#4ECDC4')

    plt.title('营销费用与订单金额时间趋势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('金额（元）', fontsize=12)
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('charts/营销趋势折线图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表2: 散点图 - 营销费用与订单金额相关性
def create_marketing_scatter_chart():
    """创建营销相关性散点图"""
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'],
                         c=marketing_df['点击量'], s=marketing_df['展现量']/500,
                         alpha=0.7, cmap='viridis')
    plt.colorbar(scatter, label='点击量')
    plt.title('营销费用与订单金额相关性分析\n(气泡大小表示展现量，颜色表示点击量)',
              fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('营销费用（元）', fontsize=12)
    plt.ylabel('订单金额（元）', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'], 1)
    p = np.poly1d(z)
    plt.plot(marketing_df['营销费用（元）'], p(marketing_df['营销费用（元）']),
             "r--", alpha=0.8, linewidth=2, label='趋势线')
    plt.legend()
    plt.tight_layout()
    plt.savefig('charts/营销相关性散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表3: 热力图 - 各指标相关性矩阵
def create_marketing_heatmap():
    """创建营销指标热力图"""
    plt.figure(figsize=(12, 10))
    correlation_cols = ['营销费用（元）', '展现量', '点击量', '订单金额（元）',
                       '加购数', '下单新客数', '访问页面数', '进店数', '商品关注数']
    corr_matrix = marketing_df[correlation_cols].corr()

    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')
    plt.title('营销指标相关性热力图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('charts/营销指标热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 执行营销数据可视化
create_marketing_trend_chart()
create_marketing_scatter_chart()
create_marketing_heatmap()
print("✓ 营销和产品销售表图表生成完成")

# ==================== 数据集2: 国内生产总值季度数据 ====================
print("\n2. 国内生产总值季度数据 - 生成3个图表")
gdp_df = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')

# 图表4: 面积图 - 各产业增加值时间变化
def create_gdp_area_chart():
    """创建GDP产业结构面积图"""
    plt.figure(figsize=(14, 8))
    quarters = gdp_df.columns[1:]
    primary = gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
    secondary = gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
    tertiary = gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）'].iloc[0, 1:].values.astype(float)

    x = np.arange(len(quarters))
    plt.fill_between(x, 0, primary, alpha=0.7, label='第一产业', color='#8FBC8F')
    plt.fill_between(x, primary, primary + secondary, alpha=0.7, label='第二产业', color='#87CEEB')
    plt.fill_between(x, primary + secondary, primary + secondary + tertiary,
                    alpha=0.7, label='第三产业', color='#DDA0DD')

    plt.title('中国GDP三大产业结构变化趋势（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('增加值（亿元）', fontsize=12)
    plt.legend(loc='upper left', fontsize=11)
    plt.xticks(x[::2], [q.replace('年第', 'Q').replace('季度', '') for q in quarters[::2]], rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts/GDP产业结构面积图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表5: 簇状柱形图 - 不同季度GDP总量比较
def create_gdp_bar_chart():
    """创建GDP季度对比柱状图"""
    plt.figure(figsize=(12, 8))
    gdp_total = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）'].iloc[0, 1:].values.astype(float)
    years = ['2019', '2020', '2021', '2022']
    quarters_per_year = ['Q1', 'Q2', 'Q3', 'Q4']

    # 重新组织数据
    gdp_by_year = gdp_total.reshape(4, 4)
    x = np.arange(len(quarters_per_year))
    width = 0.2

    for i, year in enumerate(years):
        plt.bar(x + i*width, gdp_by_year[3-i], width, label=year, alpha=0.8)

    plt.title('中国季度GDP总量对比（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('GDP（亿元）', fontsize=12)
    plt.xticks(x + width*1.5, quarters_per_year)
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts/GDP季度对比柱状图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表6: 雷达图 - 三大产业结构特征
def create_gdp_radar_chart():
    """创建GDP产业结构雷达图"""
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

    # 数据准备
    categories = ['第一产业', '第二产业', '第三产业']
    values_2022q4 = [
        gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2022年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2022年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2022年第四季度'].values[0]
    ]

    values_2021q4 = [
        gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2021年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2021年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2021年第四季度'].values[0]
    ]

    # 角度设置
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    values_2022q4 += values_2022q4[:1]  # 闭合图形
    values_2021q4 += values_2021q4[:1]
    angles += angles[:1]

    ax.plot(angles, values_2022q4, 'o-', linewidth=2, label='2022年Q4', color='#FF6B6B')
    ax.fill(angles, values_2022q4, alpha=0.25, color='#FF6B6B')
    ax.plot(angles, values_2021q4, 'o-', linewidth=2, label='2021年Q4', color='#4ECDC4')
    ax.fill(angles, values_2021q4, alpha=0.25, color='#4ECDC4')

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=12)
    ax.set_title('三大产业增加值结构对比雷达图', fontsize=16, fontweight='bold', pad=30)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    plt.tight_layout()
    plt.savefig('charts/GDP产业结构雷达图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 执行GDP数据可视化
create_gdp_area_chart()
create_gdp_bar_chart()
create_gdp_radar_chart()
print("✓ 国内生产总值季度数据图表生成完成")

# ==================== 数据集3: 某公司产品销售数据 ====================
print("\n3. 某公司产品销售数据 - 生成3个图表")
sales_df = pd.read_excel('数据可视化数据集-A/某公司产品销售数据.xlsx')

# 图表7: 簇状柱形图 - 不同地区各季度销售额对比
def create_sales_bar_chart():
    """创建地区季度销售对比柱状图"""
    plt.figure(figsize=(12, 8))
    pivot_data = sales_df.pivot(index='季度', columns='地区', values='销售额（万元）')
    pivot_data.plot(kind='bar', width=0.8, alpha=0.8)
    plt.title('各地区季度销售额对比分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.legend(title='地区', fontsize=11, title_fontsize=12)
    plt.xticks(rotation=0)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts/地区季度销售对比柱状图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表8: 饼图 - 各地区销售额占比
def create_sales_pie_chart():
    """创建地区销售占比饼图"""
    plt.figure(figsize=(10, 8))
    region_total = sales_df.groupby('地区')['销售额（万元）'].sum()
    colors = ['#FF9999', '#66B2FF', '#99FF99']
    wedges, texts, autotexts = plt.pie(region_total.values, labels=region_total.index,
                                      autopct='%1.1f%%', startangle=90, colors=colors,
                                      explode=(0.05, 0.05, 0.05), shadow=True)

    # 美化文本
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    for text in texts:
        text.set_fontsize(12)
        text.set_fontweight('bold')

    plt.title('各地区销售额占比分析', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')
    plt.tight_layout()
    plt.savefig('charts/地区销售占比饼图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表9: 箱形图 - 各地区销售额分布特征
def create_sales_box_chart():
    """创建地区销售分布箱形图"""
    plt.figure(figsize=(10, 8))
    box_data = [sales_df[sales_df['地区'] == region]['销售额（万元）'].values
               for region in sales_df['地区'].unique()]
    box_plot = plt.boxplot(box_data, labels=sales_df['地区'].unique(), patch_artist=True)

    # 设置箱形图颜色
    colors = ['#FFB6C1', '#87CEFA', '#98FB98']
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.title('各地区销售额分布特征分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('地区', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts/地区销售分布箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 执行销售数据可视化
create_sales_bar_chart()
create_sales_pie_chart()
create_sales_box_chart()
print("✓ 某公司产品销售数据图表生成完成")

# ==================== 数据分析统计 ====================
def generate_analysis_summary():
    """生成分析摘要"""
    print("\n" + "="*60)
    print("数据可视化分析完成！")
    print("="*60)
    print("共生成9个图表:")
    print("1. 营销趋势折线图.png - 展示营销费用与订单金额的时间趋势")
    print("2. 营销相关性散点图.png - 分析营销投入与收益的相关性")
    print("3. 营销指标热力图.png - 展示各营销指标间的相关性")
    print("4. GDP产业结构面积图.png - 展示三大产业的时间变化趋势")
    print("5. GDP季度对比柱状图.png - 比较不同年份的季度GDP")
    print("6. GDP产业结构雷达图.png - 对比不同年份的产业结构")
    print("7. 地区季度销售对比柱状图.png - 比较各地区季度销售表现")
    print("8. 地区销售占比饼图.png - 展示各地区销售额占比")
    print("9. 地区销售分布箱形图.png - 分析各地区销售额分布特征")
    print("\n所有图表已保存到 'charts' 文件夹中")

    # 计算关键统计指标
    marketing_corr = marketing_df['营销费用（元）'].corr(marketing_df['订单金额（元）'])
    roi = marketing_df['订单金额（元）'].sum() / marketing_df['营销费用（元）'].sum()

    print(f"\n关键发现:")
    print(f"- 营销ROI: {roi:.2f}")
    print(f"- 营销费用与订单金额相关性: {marketing_corr:.3f}")

    # GDP分析
    total_gdp = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）']['2022年第四季度'].values[0]
    tertiary_gdp = gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2022年第四季度'].values[0]
    tertiary_ratio = (tertiary_gdp / total_gdp) * 100
    print(f"- 第三产业占GDP比重: {tertiary_ratio:.1f}%")

    # 销售分析
    region_total = sales_df.groupby('地区')['销售额（万元）'].sum()
    best_region = region_total.idxmax()
    best_ratio = (region_total.max() / region_total.sum()) * 100
    print(f"- 最佳销售地区: {best_region} ({best_ratio:.1f}%)")

# 执行分析摘要
generate_analysis_summary()

if __name__ == "__main__":
    print("\n程序执行完成！所有图表和分析结果已生成。")
