# 中文字体显示问题修复指南

## 问题描述
在matplotlib生成的图表中，中文文字显示为方框（□□□）或乱码，这是因为matplotlib默认不支持中文字体。

## 解决方案

### 方案一：使用修复版代码（推荐）
直接运行 `visualization_fixed.py` 文件，该文件已经包含了完整的中文字体配置：

```bash
python visualization_fixed.py
```

### 方案二：手动配置字体
在任何matplotlib代码开头添加以下配置：

```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

def setup_chinese_fonts():
    """配置中文字体显示"""
    # 清除字体缓存
    try:
        fm._rebuild()
    except:
        pass
    
    # 根据系统选择字体
    system = platform.system()
    if system == 'Windows':
        font_candidates = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']
    elif system == 'Darwin':  # macOS
        font_candidates = ['PingFang SC', 'Heiti SC', 'STHeiti']
    else:  # Linux
        font_candidates = ['WenQuanYi Micro Hei', 'Noto Sans CJK SC']
    
    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    selected_font = None
    
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break
    
    # 设置字体参数
    if selected_font:
        print(f"✓ 选择中文字体: {selected_font}")
        plt.rcParams.update({
            'font.sans-serif': [selected_font] + font_candidates + ['DejaVu Sans'],
            'axes.unicode_minus': False,
            'font.family': 'sans-serif',
            'font.size': 12
        })
    else:
        print("⚠ 使用备选字体配置")
        plt.rcParams.update({
            'font.sans-serif': font_candidates + ['DejaVu Sans'],
            'axes.unicode_minus': False
        })
    
    return selected_font

# 在绘图前调用
setup_chinese_fonts()
```

### 方案三：简化配置（适用于Windows）
如果您使用Windows系统，可以使用简化配置：

```python
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
plt.rcParams['axes.unicode_minus'] = False
```

## 验证字体配置
运行以下测试代码验证中文字体是否正常显示：

```python
import matplotlib.pyplot as plt
import numpy as np

# 配置中文字体（使用上述任一方案）
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建测试图表
plt.figure(figsize=(8, 6))
x = ['北京市', '上海市', '广州市', '深圳市']
y = [100, 85, 75, 90]
plt.bar(x, y, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
plt.title('中文字体测试图表', fontsize=16)
plt.xlabel('城市名称', fontsize=12)
plt.ylabel('数值', fontsize=12)
plt.show()
```

## 常见问题及解决方法

### 问题1：仍然显示方框
**解决方法：**
1. 重启Python环境
2. 清除matplotlib缓存：
   ```python
   import matplotlib.font_manager as fm
   fm._rebuild()
   ```
3. 检查系统是否安装了中文字体

### 问题2：Linux系统中文字体缺失
**解决方法：**
安装中文字体包：
```bash
# Ubuntu/Debian
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei

# CentOS/RHEL
sudo yum install wqy-microhei-fonts wqy-zenhei-fonts
```

### 问题3：macOS中文字体问题
**解决方法：**
使用系统自带的中文字体：
```python
plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Heiti SC', 'STHeiti']
```

## 字体优先级说明

### Windows系统推荐字体：
1. **Microsoft YaHei** (微软雅黑) - 现代、清晰
2. **SimHei** (黑体) - 经典、粗体
3. **SimSun** (宋体) - 传统、细体
4. **KaiTi** (楷体) - 手写风格

### macOS系统推荐字体：
1. **PingFang SC** (苹方) - 现代设计
2. **Heiti SC** (黑体) - 标准黑体
3. **STHeiti** (华文黑体) - 传统黑体

### Linux系统推荐字体：
1. **WenQuanYi Micro Hei** (文泉驿微米黑) - 开源字体
2. **Noto Sans CJK SC** (思源黑体) - Google字体
3. **Source Han Sans SC** (思源黑体) - Adobe字体

## 最终检查清单

✅ **已修复的文件：**
- `visualization_fixed.py` - 包含完整字体配置的可视化代码
- `charts_fixed/` 文件夹 - 包含9个修复后的图表

✅ **验证步骤：**
1. 运行 `python visualization_fixed.py`
2. 检查控制台输出是否显示 "✓ 选择中文字体: Microsoft YaHei"
3. 查看生成的图表是否正确显示中文
4. 检查 `charts_fixed/中文字体测试.png` 文件

✅ **如果问题仍然存在：**
1. 确认系统已安装中文字体
2. 重启Python环境
3. 使用 `python check_fonts.py` 检查可用字体
4. 联系技术支持

## 总结
通过以上方法，您的matplotlib图表应该能够正确显示中文了。推荐使用 `visualization_fixed.py` 文件，它包含了最完整和可靠的中文字体配置。
