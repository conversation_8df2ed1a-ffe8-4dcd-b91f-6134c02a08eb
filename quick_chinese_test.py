"""
快速中文字体测试
生成一个简单的测试图表来验证中文显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import platform
import os

print("快速中文字体测试")
print("="*40)

# 获取中文字体
def get_chinese_font():
    system = platform.system()
    
    if system == 'Windows':
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',  # 微软雅黑
            r'C:\Windows\Fonts\simhei.ttf',  # 黑体
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    return FontProperties(fname=font_path)
                except:
                    continue
    
    # 备选方案
    font_names = ['Microsoft YaHei', 'SimHei', 'SimSun']
    for font_name in font_names:
        try:
            return FontProperties(family=font_name)
        except:
            continue
    
    return None

# 配置字体
chinese_font = get_chinese_font()
print(f"中文字体配置: {'成功' if chinese_font else '失败'}")

# 设置matplotlib参数
plt.rcParams.update({
    'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans'],
    'axes.unicode_minus': False,
    'font.family': 'sans-serif'
})

# 创建测试图表
plt.figure(figsize=(10, 6))

# 测试数据
cities = ['北京市', '上海市', '广州市', '深圳市']
sales = [1200, 1000, 800, 900]
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

# 创建柱状图
bars = plt.bar(cities, sales, color=colors, alpha=0.8)

# 方法1: 使用字体对象设置文字
if chinese_font:
    plt.title('中文字体测试图表 - 各城市销售数据', fontproperties=chinese_font, fontsize=16, fontweight='bold')
    plt.xlabel('城市名称', fontproperties=chinese_font, fontsize=12)
    plt.ylabel('销售额（万元）', fontproperties=chinese_font, fontsize=12)
    
    # 设置x轴标签
    ax = plt.gca()
    ax.set_xticklabels(cities, fontproperties=chinese_font)
    
    # 添加数值标签
    for bar, value in zip(bars, sales):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20,
                f'{value}万', ha='center', va='bottom', fontsize=10,
                fontproperties=chinese_font)
else:
    # 方法2: 使用rcParams设置
    plt.title('中文字体测试图表 - 各城市销售数据', fontsize=16, fontweight='bold')
    plt.xlabel('城市名称', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    
    # 添加数值标签
    for bar, value in zip(bars, sales):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20,
                f'{value}万', ha='center', va='bottom', fontsize=10)

plt.grid(True, alpha=0.3, axis='y')
plt.tight_layout()

# 保存图表
plt.savefig('quick_chinese_test.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n测试完成！")
print("请查看生成的 'quick_chinese_test.png' 文件")
print("如果中文显示正常，说明字体配置成功")
print("如果显示为方框，请尝试以下解决方案：")
print("1. 重启Python环境")
print("2. 使用 visualization_final_fix.py 文件")
print("3. 检查系统是否安装了中文字体")

# 显示当前字体配置
print(f"\n当前字体配置:")
print(f"font.sans-serif: {plt.rcParams['font.sans-serif']}")
print(f"使用字体对象: {'是' if chinese_font else '否'}")

# 创建对比测试
print("\n生成对比测试图...")
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 左图：使用字体对象
ax1.bar(cities, sales, color=colors, alpha=0.8)
if chinese_font:
    ax1.set_title('方法1：使用字体对象', fontproperties=chinese_font, fontsize=14)
    ax1.set_xlabel('城市', fontproperties=chinese_font)
    ax1.set_ylabel('销售额（万元）', fontproperties=chinese_font)
    ax1.set_xticklabels(cities, fontproperties=chinese_font)
else:
    ax1.set_title('方法1：使用字体对象（失败）', fontsize=14)
    ax1.set_xlabel('城市')
    ax1.set_ylabel('销售额（万元）')

# 右图：使用rcParams
ax2.bar(cities, sales, color=colors, alpha=0.8)
ax2.set_title('方法2：使用rcParams设置', fontsize=14)
ax2.set_xlabel('城市')
ax2.set_ylabel('销售额（万元）')

plt.tight_layout()
plt.savefig('chinese_font_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

print("对比测试完成！请查看 'chinese_font_comparison.png' 文件")
