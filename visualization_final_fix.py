"""
数据可视化期末考核 - 最终完美中文字体修复版
针对每个图表元素精确设置中文字体
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import seaborn as sns
import platform
import os
import warnings
warnings.filterwarnings('ignore')

# ==================== 最强中文字体配置 ====================
def get_chinese_font():
    """获取中文字体对象"""
    system = platform.system()

    if system == 'Windows':
        # Windows系统字体文件路径
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',  # 微软雅黑
            r'C:\Windows\Fonts\simhei.ttf',  # 黑体
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    return FontProperties(fname=font_path)
                except:
                    continue

    # 备选方案：使用系统字体名称
    font_names = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS']
    for font_name in font_names:
        try:
            return FontProperties(family=font_name)
        except:
            continue

    return None

# 获取中文字体
chinese_font = get_chinese_font()
print(f"中文字体配置: {'成功' if chinese_font else '失败'}")

# 设置matplotlib全局参数
plt.rcParams.update({
    'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans'],
    'axes.unicode_minus': False,
    'font.family': 'sans-serif',
    'font.size': 12
})

def apply_chinese_font_to_all_elements(ax, title=None):
    """对图表的所有文字元素应用中文字体"""
    if not chinese_font:
        return

    # 设置标题
    if title:
        ax.set_title(title, fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)
    elif ax.get_title():
        ax.set_title(ax.get_title(), fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)

    # 设置轴标签
    if ax.get_xlabel():
        ax.set_xlabel(ax.get_xlabel(), fontproperties=chinese_font, fontsize=12)
    if ax.get_ylabel():
        ax.set_ylabel(ax.get_ylabel(), fontproperties=chinese_font, fontsize=12)

    # 设置刻度标签
    for label in ax.get_xticklabels():
        label.set_fontproperties(chinese_font)
    for label in ax.get_yticklabels():
        label.set_fontproperties(chinese_font)

    # 设置图例
    legend = ax.get_legend()
    if legend:
        for text in legend.get_texts():
            text.set_fontproperties(chinese_font)

# 创建输出目录
if not os.path.exists('charts_final'):
    os.makedirs('charts_final')

print("开始生成最终修复版数据可视化图表...")
print("="*60)

# ==================== 数据集1: 营销和产品销售表 ====================
print("\n1. 营销和产品销售表 - 生成3个图表")
marketing_df = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
marketing_df['日期'] = pd.to_datetime(marketing_df['日期'])

# 图表1: 折线图
plt.figure(figsize=(12, 6))
plt.plot(marketing_df['日期'], marketing_df['营销费用（元）'],
         marker='o', linewidth=2, label='营销费用', color='#FF6B6B')
plt.plot(marketing_df['日期'], marketing_df['订单金额（元）'],
         marker='s', linewidth=2, label='订单金额', color='#4ECDC4')

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '营销费用与订单金额时间趋势分析')
ax.set_xlabel('日期', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('金额（元）', fontproperties=chinese_font, fontsize=12)

# 手动设置图例
legend = plt.legend(['营销费用', '订单金额'], fontsize=11)
if chinese_font:
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)

plt.grid(True, alpha=0.3)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('charts_final/营销趋势折线图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表2: 散点图
plt.figure(figsize=(10, 8))
scatter = plt.scatter(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'],
                     c=marketing_df['点击量'], s=marketing_df['展现量']/500,
                     alpha=0.7, cmap='viridis')
cbar = plt.colorbar(scatter)
if chinese_font:
    cbar.set_label('点击量', fontproperties=chinese_font, fontsize=12)

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '营销费用与订单金额相关性分析\n(气泡大小表示展现量，颜色表示点击量)')
ax.set_xlabel('营销费用（元）', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('订单金额（元）', fontproperties=chinese_font, fontsize=12)

# 添加趋势线
z = np.polyfit(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'], 1)
p = np.poly1d(z)
plt.plot(marketing_df['营销费用（元）'], p(marketing_df['营销费用（元）']),
         "r--", alpha=0.8, linewidth=2, label='趋势线')

legend = plt.legend(['趋势线'])
if chinese_font:
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)

plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('charts_final/营销相关性散点图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表3: 热力图
plt.figure(figsize=(12, 10))
correlation_cols = ['营销费用（元）', '展现量', '点击量', '订单金额（元）',
                   '加购数', '下单新客数', '访问页面数', '进店数', '商品关注数']
corr_matrix = marketing_df[correlation_cols].corr()

mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
ax = sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

# 手动设置热力图的标签
if chinese_font:
    ax.set_title('营销指标相关性热力图', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)
    # 设置x和y轴标签
    ax.set_xticklabels(correlation_cols, fontproperties=chinese_font, rotation=45, ha='right')
    ax.set_yticklabels(correlation_cols, fontproperties=chinese_font, rotation=0)

plt.tight_layout()
plt.savefig('charts_final/营销指标热力图.png', dpi=300, bbox_inches='tight')
plt.show()

print("✓ 营销和产品销售表图表生成完成")

# ==================== 数据集2: 国内生产总值季度数据 ====================
print("\n2. 国内生产总值季度数据 - 生成3个图表")
gdp_df = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')

# 图表4: 面积图
plt.figure(figsize=(14, 8))
quarters = gdp_df.columns[1:]
primary = gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
secondary = gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
tertiary = gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）'].iloc[0, 1:].values.astype(float)

x = np.arange(len(quarters))
plt.fill_between(x, 0, primary, alpha=0.7, label='第一产业', color='#8FBC8F')
plt.fill_between(x, primary, primary + secondary, alpha=0.7, label='第二产业', color='#87CEEB')
plt.fill_between(x, primary + secondary, primary + secondary + tertiary,
                alpha=0.7, label='第三产业', color='#DDA0DD')

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '中国GDP三大产业结构变化趋势（2019-2022）')
ax.set_xlabel('季度', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('增加值（亿元）', fontproperties=chinese_font, fontsize=12)

# 手动设置图例
legend = plt.legend(['第一产业', '第二产业', '第三产业'], loc='upper left', fontsize=11)
if chinese_font:
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)

plt.xticks(x[::2], [q.replace('年第', 'Q').replace('季度', '') for q in quarters[::2]], rotation=45)
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('charts_final/GDP产业结构面积图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表5: 簇状柱形图
plt.figure(figsize=(12, 8))
gdp_total = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）'].iloc[0, 1:].values.astype(float)
years = ['2019', '2020', '2021', '2022']
quarters_per_year = ['Q1', 'Q2', 'Q3', 'Q4']

gdp_by_year = gdp_total.reshape(4, 4)
x = np.arange(len(quarters_per_year))
width = 0.2

colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
for i, year in enumerate(years):
    plt.bar(x + i*width, gdp_by_year[3-i], width, label=year, alpha=0.8, color=colors[i])

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '中国季度GDP总量对比（2019-2022）')
ax.set_xlabel('季度', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('GDP（亿元）', fontproperties=chinese_font, fontsize=12)

plt.xticks(x + width*1.5, quarters_per_year)
legend = plt.legend(years, fontsize=11)
if chinese_font:
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)

plt.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('charts_final/GDP季度对比柱状图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表6: 雷达图
fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

categories = ['第一产业', '第二产业', '第三产业']
values_2022q4 = [
    gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2022年第四季度'].values[0],
    gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2022年第四季度'].values[0],
    gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2022年第四季度'].values[0]
]

values_2021q4 = [
    gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2021年第四季度'].values[0],
    gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2021年第四季度'].values[0],
    gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2021年第四季度'].values[0]
]

angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
values_2022q4 += values_2022q4[:1]
values_2021q4 += values_2021q4[:1]
angles += angles[:1]

ax.plot(angles, values_2022q4, 'o-', linewidth=2, label='2022年Q4', color='#FF6B6B')
ax.fill(angles, values_2022q4, alpha=0.25, color='#FF6B6B')
ax.plot(angles, values_2021q4, 'o-', linewidth=2, label='2021年Q4', color='#4ECDC4')
ax.fill(angles, values_2021q4, alpha=0.25, color='#4ECDC4')

ax.set_xticks(angles[:-1])
if chinese_font:
    ax.set_xticklabels(categories, fontproperties=chinese_font, fontsize=12)
    ax.set_title('三大产业增加值结构对比雷达图', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=30)
    legend = ax.legend(['2022年Q4', '2021年Q4'], loc='upper right', bbox_to_anchor=(1.3, 1.0))
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)
else:
    ax.set_xticklabels(categories, fontsize=12)
    ax.set_title('三大产业增加值结构对比雷达图', fontsize=16, fontweight='bold', pad=30)
    ax.legend(['2022年Q4', '2021年Q4'], loc='upper right', bbox_to_anchor=(1.3, 1.0))

plt.tight_layout()
plt.savefig('charts_final/GDP产业结构雷达图.png', dpi=300, bbox_inches='tight')
plt.show()

print("✓ 国内生产总值季度数据图表生成完成")

# ==================== 数据集3: 某公司产品销售数据 ====================
print("\n3. 某公司产品销售数据 - 生成3个图表")
sales_df = pd.read_excel('数据可视化数据集-A/某公司产品销售数据.xlsx')

# 图表7: 簇状柱形图
plt.figure(figsize=(12, 8))
pivot_data = sales_df.pivot(index='季度', columns='地区', values='销售额（万元）')

x = np.arange(len(pivot_data.index))
width = 0.25
regions = pivot_data.columns
colors = ['#FF9999', '#66B2FF', '#99FF99']

for i, region in enumerate(regions):
    plt.bar(x + i*width, pivot_data[region], width, label=region, alpha=0.8, color=colors[i])

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '各地区季度销售额对比分析')
ax.set_xlabel('季度', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('销售额（万元）', fontproperties=chinese_font, fontsize=12)

plt.xticks(x + width, pivot_data.index)
legend = plt.legend(regions, title='地区', fontsize=11, title_fontsize=12)
if chinese_font:
    legend.set_title('地区', prop=chinese_font)
    for text in legend.get_texts():
        text.set_fontproperties(chinese_font)

plt.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('charts_final/地区季度销售对比柱状图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表8: 饼图
plt.figure(figsize=(10, 8))
region_total = sales_df.groupby('地区')['销售额（万元）'].sum()
colors = ['#FF9999', '#66B2FF', '#99FF99']

wedges, texts, autotexts = plt.pie(region_total.values, labels=region_total.index,
                                  autopct='%1.1f%%', startangle=90, colors=colors,
                                  explode=(0.05, 0.05, 0.05), shadow=True)

if chinese_font:
    plt.title('各地区销售额占比分析', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)
    for text in texts:
        text.set_fontproperties(chinese_font)
        text.set_fontsize(12)
        text.set_fontweight('bold')
else:
    plt.title('各地区销售额占比分析', fontsize=16, fontweight='bold', pad=20)

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')
    autotext.set_fontsize(12)

plt.axis('equal')
plt.tight_layout()
plt.savefig('charts_final/地区销售占比饼图.png', dpi=300, bbox_inches='tight')
plt.show()

# 图表9: 箱形图
plt.figure(figsize=(10, 8))
box_data = [sales_df[sales_df['地区'] == region]['销售额（万元）'].values
           for region in sales_df['地区'].unique()]
box_plot = plt.boxplot(box_data, labels=sales_df['地区'].unique(), patch_artist=True)

colors = ['#FFB6C1', '#87CEFA', '#98FB98']
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.7)

ax = plt.gca()
apply_chinese_font_to_all_elements(ax, '各地区销售额分布特征分析')
ax.set_xlabel('地区', fontproperties=chinese_font, fontsize=12)
ax.set_ylabel('销售额（万元）', fontproperties=chinese_font, fontsize=12)

# 手动设置x轴标签
if chinese_font:
    ax.set_xticklabels(sales_df['地区'].unique(), fontproperties=chinese_font)

plt.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('charts_final/地区销售分布箱形图.png', dpi=300, bbox_inches='tight')
plt.show()

print("✓ 某公司产品销售数据图表生成完成")

# ==================== 最终测试 ====================
print("\n" + "="*60)
print("最终修复版数据可视化分析完成！")
print("="*60)
print("共生成9个图表（精确修复每个文字元素）:")
print("1. 营销趋势折线图.png")
print("2. 营销相关性散点图.png")
print("3. 营销指标热力图.png")
print("4. GDP产业结构面积图.png")
print("5. GDP季度对比柱状图.png")
print("6. GDP产业结构雷达图.png")
print("7. 地区季度销售对比柱状图.png")
print("8. 地区销售占比饼图.png")
print("9. 地区销售分布箱形图.png")
print(f"\n所有图表已保存到 'charts_final' 文件夹中")
print(f"中文字体状态: {'已配置' if chinese_font else '使用系统默认'}")

# 创建最终验证图
print("\n正在生成最终验证图...")
plt.figure(figsize=(12, 8))
test_cities = ['北京市', '上海市', '广州市', '深圳市', '成都市', '杭州市']
test_sales = [1193, 1051, 890, 950, 820, 760]
bars = plt.bar(test_cities, test_sales, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3'])

if chinese_font:
    plt.title('最终中文字体验证图 - 全国主要城市销售数据', fontproperties=chinese_font, fontsize=16, fontweight='bold')
    plt.xlabel('城市名称', fontproperties=chinese_font, fontsize=12)
    plt.ylabel('销售额（万元）', fontproperties=chinese_font, fontsize=12)

    ax = plt.gca()
    ax.set_xticklabels(test_cities, fontproperties=chinese_font, rotation=45)

    # 添加数值标签
    for bar, value in zip(bars, test_sales):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20,
                f'{value}万元', ha='center', va='bottom', fontsize=10,
                fontproperties=chinese_font)
else:
    plt.title('最终中文字体验证图 - 全国主要城市销售数据', fontsize=16, fontweight='bold')
    plt.xlabel('城市名称', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.xticks(rotation=45)

plt.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('charts_final/最终中文字体验证图.png', dpi=300, bbox_inches='tight')
plt.show()

print("✓ 最终验证完成！")
print("\n📋 检查清单:")
print("1. 查看 charts_final 文件夹中的所有图片")
print("2. 确认每个图表的中文文字是否正常显示")
print("3. 如果仍有问题，请告诉我具体哪些图表还有问题")
