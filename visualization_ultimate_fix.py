"""
数据可视化期末考核 - 终极中文字体修复版
使用字体文件路径直接指定中文字体，确保100%显示中文
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.font_manager import FontProperties
import seaborn as sns
import platform
import os
import warnings
warnings.filterwarnings('ignore')

# ==================== 终极中文字体配置 ====================
def setup_ultimate_chinese_fonts():
    """终极中文字体配置 - 使用字体文件路径"""
    print("正在配置终极中文字体解决方案...")

    # 清除matplotlib设置
    plt.rcParams.clear()

    # 根据系统查找字体文件
    system = platform.system()
    font_prop = None
    selected_font_name = None

    if system == 'Windows':
        # Windows系统字体文件路径
        font_files = [
            (r'C:\Windows\Fonts\msyh.ttc', 'Microsoft YaHei'),  # 微软雅黑
            (r'C:\Windows\Fonts\simhei.ttf', 'SimHei'),         # 黑体
            (r'C:\Windows\Fonts\simsun.ttc', 'SimSun'),         # 宋体
            (r'C:\Windows\Fonts\kaiti.ttf', 'KaiTi'),           # 楷体
        ]
    elif system == 'Darwin':  # macOS
        font_files = [
            ('/System/Library/Fonts/PingFang.ttc', 'PingFang SC'),
            ('/System/Library/Fonts/Helvetica.ttc', 'Heiti SC'),
        ]
    else:  # Linux
        font_files = [
            ('/usr/share/fonts/truetype/wqy/wqy-microhei.ttc', 'WenQuanYi Micro Hei'),
        ]

    # 查找可用的字体文件
    for font_path, font_name in font_files:
        if os.path.exists(font_path):
            try:
                font_prop = FontProperties(fname=font_path)
                selected_font_name = font_name
                print(f"✓ 找到字体文件: {font_path}")
                print(f"✓ 字体名称: {font_name}")
                break
            except Exception as e:
                print(f"✗ 字体文件加载失败 {font_path}: {e}")
                continue

    if font_prop is None:
        print("⚠ 未找到字体文件，尝试系统字体名称...")
        # 备选方案：使用系统字体名称
        font_names = ['Microsoft YaHei', 'SimHei', 'SimSun', 'Arial Unicode MS']
        for font_name in font_names:
            try:
                font_prop = FontProperties(family=font_name)
                selected_font_name = font_name
                print(f"✓ 使用系统字体: {font_name}")
                break
            except:
                continue

    # 设置matplotlib参数（作为备选）
    plt.rcParams.update({
        'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans'],
        'axes.unicode_minus': False,
        'font.family': 'sans-serif',
        'font.size': 12,
        'figure.titlesize': 16,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'legend.fontsize': 11
    })

    return font_prop, selected_font_name

# 配置字体
chinese_font, font_name = setup_ultimate_chinese_fonts()

# 创建绘图函数，确保所有文字都使用中文字体
def plot_with_chinese(fig_func):
    """装饰器：确保图表中所有文字都使用中文字体"""
    def wrapper(*args, **kwargs):
        result = fig_func(*args, **kwargs)

        # 如果有中文字体，应用到所有文字元素
        if chinese_font:
            ax = plt.gca()
            # 设置标题
            if ax.get_title():
                ax.set_title(ax.get_title(), fontproperties=chinese_font)
            # 设置轴标签
            if ax.get_xlabel():
                ax.set_xlabel(ax.get_xlabel(), fontproperties=chinese_font)
            if ax.get_ylabel():
                ax.set_ylabel(ax.get_ylabel(), fontproperties=chinese_font)
            # 设置刻度标签
            for label in ax.get_xticklabels():
                label.set_fontproperties(chinese_font)
            for label in ax.get_yticklabels():
                label.set_fontproperties(chinese_font)
            # 设置图例
            legend = ax.get_legend()
            if legend:
                for text in legend.get_texts():
                    text.set_fontproperties(chinese_font)

        return result
    return wrapper

# 设置图表样式
plt.style.use('default')  # 使用默认样式避免冲突
sns.set_palette("husl")

# 创建输出目录
if not os.path.exists('charts_ultimate'):
    os.makedirs('charts_ultimate')

print("开始生成终极修复版数据可视化图表...")
print("="*60)

# ==================== 数据集1: 营销和产品销售表 ====================
print("\n1. 营销和产品销售表 - 生成3个图表")
marketing_df = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
marketing_df['日期'] = pd.to_datetime(marketing_df['日期'])

# 图表1: 折线图 - 营销费用与订单金额时间趋势
@plot_with_chinese
def create_marketing_trend():
    plt.figure(figsize=(12, 6))
    plt.plot(marketing_df['日期'], marketing_df['营销费用（元）'],
             marker='o', linewidth=2, label='营销费用', color='#FF6B6B')
    plt.plot(marketing_df['日期'], marketing_df['订单金额（元）'],
             marker='s', linewidth=2, label='订单金额', color='#4ECDC4')

    plt.title('营销费用与订单金额时间趋势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('金额（元）', fontsize=12)
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('charts_ultimate/营销趋势折线图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_marketing_trend()

# 图表2: 散点图 - 营销费用与订单金额相关性
@plot_with_chinese
def create_marketing_scatter():
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'],
                         c=marketing_df['点击量'], s=marketing_df['展现量']/500,
                         alpha=0.7, cmap='viridis')
    plt.colorbar(scatter, label='点击量')
    plt.title('营销费用与订单金额相关性分析\n(气泡大小表示展现量，颜色表示点击量)',
              fontsize=14, fontweight='bold', pad=20)
    plt.xlabel('营销费用（元）', fontsize=12)
    plt.ylabel('订单金额（元）', fontsize=12)
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(marketing_df['营销费用（元）'], marketing_df['订单金额（元）'], 1)
    p = np.poly1d(z)
    plt.plot(marketing_df['营销费用（元）'], p(marketing_df['营销费用（元）']),
             "r--", alpha=0.8, linewidth=2, label='趋势线')
    plt.legend()
    plt.tight_layout()
    plt.savefig('charts_ultimate/营销相关性散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_marketing_scatter()

# 图表3: 热力图 - 各指标相关性矩阵
def create_marketing_heatmap():
    plt.figure(figsize=(12, 10))
    correlation_cols = ['营销费用（元）', '展现量', '点击量', '订单金额（元）',
                       '加购数', '下单新客数', '访问页面数', '进店数', '商品关注数']
    corr_matrix = marketing_df[correlation_cols].corr()

    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdYlBu_r', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')

    # 手动设置标题和标签字体
    if chinese_font:
        plt.title('营销指标相关性热力图', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)
        # 设置坐标轴标签字体
        ax = plt.gca()
        ax.set_xticklabels(ax.get_xticklabels(), fontproperties=chinese_font)
        ax.set_yticklabels(ax.get_yticklabels(), fontproperties=chinese_font)
    else:
        plt.title('营销指标相关性热力图', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('charts_ultimate/营销指标热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_marketing_heatmap()

print("✓ 营销和产品销售表图表生成完成")

# ==================== 数据集2: 国内生产总值季度数据 ====================
print("\n2. 国内生产总值季度数据 - 生成3个图表")
gdp_df = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')

# 图表4: 面积图 - 各产业增加值时间变化
@plot_with_chinese
def create_gdp_area():
    plt.figure(figsize=(14, 8))
    quarters = gdp_df.columns[1:]
    primary = gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
    secondary = gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）'].iloc[0, 1:].values.astype(float)
    tertiary = gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）'].iloc[0, 1:].values.astype(float)

    x = np.arange(len(quarters))
    plt.fill_between(x, 0, primary, alpha=0.7, label='第一产业', color='#8FBC8F')
    plt.fill_between(x, primary, primary + secondary, alpha=0.7, label='第二产业', color='#87CEEB')
    plt.fill_between(x, primary + secondary, primary + secondary + tertiary,
                    alpha=0.7, label='第三产业', color='#DDA0DD')

    plt.title('中国GDP三大产业结构变化趋势（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('增加值（亿元）', fontsize=12)
    plt.legend(loc='upper left', fontsize=11)
    plt.xticks(x[::2], [q.replace('年第', 'Q').replace('季度', '') for q in quarters[::2]], rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts_ultimate/GDP产业结构面积图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_gdp_area()

# 图表5: 簇状柱形图 - 不同季度GDP总量比较
@plot_with_chinese
def create_gdp_bar():
    plt.figure(figsize=(12, 8))
    gdp_total = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）'].iloc[0, 1:].values.astype(float)
    years = ['2019', '2020', '2021', '2022']
    quarters_per_year = ['Q1', 'Q2', 'Q3', 'Q4']

    # 重新组织数据
    gdp_by_year = gdp_total.reshape(4, 4)
    x = np.arange(len(quarters_per_year))
    width = 0.2

    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    for i, year in enumerate(years):
        plt.bar(x + i*width, gdp_by_year[3-i], width, label=year, alpha=0.8, color=colors[i])

    plt.title('中国季度GDP总量对比（2019-2022）', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('GDP（亿元）', fontsize=12)
    plt.xticks(x + width*1.5, quarters_per_year)
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts_ultimate/GDP季度对比柱状图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_gdp_bar()

# 图表6: 雷达图 - 三大产业结构特征
def create_gdp_radar():
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

    # 数据准备
    categories = ['第一产业', '第二产业', '第三产业']
    values_2022q4 = [
        gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2022年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2022年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2022年第四季度'].values[0]
    ]

    values_2021q4 = [
        gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）']['2021年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）']['2021年第四季度'].values[0],
        gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）']['2021年第四季度'].values[0]
    ]

    # 角度设置
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    values_2022q4 += values_2022q4[:1]  # 闭合图形
    values_2021q4 += values_2021q4[:1]
    angles += angles[:1]

    ax.plot(angles, values_2022q4, 'o-', linewidth=2, label='2022年Q4', color='#FF6B6B')
    ax.fill(angles, values_2022q4, alpha=0.25, color='#FF6B6B')
    ax.plot(angles, values_2021q4, 'o-', linewidth=2, label='2021年Q4', color='#4ECDC4')
    ax.fill(angles, values_2021q4, alpha=0.25, color='#4ECDC4')

    ax.set_xticks(angles[:-1])

    # 手动设置中文标签
    if chinese_font:
        ax.set_xticklabels(categories, fontproperties=chinese_font, fontsize=12)
        ax.set_title('三大产业增加值结构对比雷达图', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=30)
        # 设置图例
        legend = ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        for text in legend.get_texts():
            text.set_fontproperties(chinese_font)
    else:
        ax.set_xticklabels(categories, fontsize=12)
        ax.set_title('三大产业增加值结构对比雷达图', fontsize=16, fontweight='bold', pad=30)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    plt.tight_layout()
    plt.savefig('charts_ultimate/GDP产业结构雷达图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_gdp_radar()

print("✓ 国内生产总值季度数据图表生成完成")

# ==================== 数据集3: 某公司产品销售数据 ====================
print("\n3. 某公司产品销售数据 - 生成3个图表")
sales_df = pd.read_excel('数据可视化数据集-A/某公司产品销售数据.xlsx')

# 图表7: 簇状柱形图 - 不同地区各季度销售额对比
@plot_with_chinese
def create_sales_bar():
    plt.figure(figsize=(12, 8))
    pivot_data = sales_df.pivot(index='季度', columns='地区', values='销售额（万元）')

    # 手动创建簇状柱形图
    x = np.arange(len(pivot_data.index))
    width = 0.25
    regions = pivot_data.columns
    colors = ['#FF9999', '#66B2FF', '#99FF99']

    for i, region in enumerate(regions):
        plt.bar(x + i*width, pivot_data[region], width, label=region, alpha=0.8, color=colors[i])

    plt.title('各地区季度销售额对比分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('季度', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.xticks(x + width, pivot_data.index)
    plt.legend(title='地区', fontsize=11, title_fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts_ultimate/地区季度销售对比柱状图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_sales_bar()

# 图表8: 饼图 - 各地区销售额占比
def create_sales_pie():
    plt.figure(figsize=(10, 8))
    region_total = sales_df.groupby('地区')['销售额（万元）'].sum()
    colors = ['#FF9999', '#66B2FF', '#99FF99']

    # 创建饼图
    wedges, texts, autotexts = plt.pie(region_total.values, labels=region_total.index,
                                      autopct='%1.1f%%', startangle=90, colors=colors,
                                      explode=(0.05, 0.05, 0.05), shadow=True)

    # 手动设置字体
    if chinese_font:
        plt.title('各地区销售额占比分析', fontproperties=chinese_font, fontsize=16, fontweight='bold', pad=20)
        for text in texts:
            text.set_fontproperties(chinese_font)
            text.set_fontsize(12)
            text.set_fontweight('bold')
    else:
        plt.title('各地区销售额占比分析', fontsize=16, fontweight='bold', pad=20)
        for text in texts:
            text.set_fontsize(12)
            text.set_fontweight('bold')

    # 美化百分比文本
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    plt.axis('equal')
    plt.tight_layout()
    plt.savefig('charts_ultimate/地区销售占比饼图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_sales_pie()

# 图表9: 箱形图 - 各地区销售额分布特征
@plot_with_chinese
def create_sales_box():
    plt.figure(figsize=(10, 8))
    box_data = [sales_df[sales_df['地区'] == region]['销售额（万元）'].values
               for region in sales_df['地区'].unique()]
    box_plot = plt.boxplot(box_data, labels=sales_df['地区'].unique(), patch_artist=True)

    # 设置箱形图颜色
    colors = ['#FFB6C1', '#87CEFA', '#98FB98']
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.title('各地区销售额分布特征分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('地区', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts_ultimate/地区销售分布箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

create_sales_box()

print("✓ 某公司产品销售数据图表生成完成")

# ==================== 最终测试和报告 ====================
print("\n" + "="*60)
print("终极修复版数据可视化分析完成！")
print("="*60)
print("共生成9个图表（使用字体文件路径确保中文显示）:")
print("1. 营销趋势折线图.png - 展示营销费用与订单金额的时间趋势")
print("2. 营销相关性散点图.png - 分析营销投入与收益的相关性")
print("3. 营销指标热力图.png - 展示各营销指标间的相关性")
print("4. GDP产业结构面积图.png - 展示三大产业的时间变化趋势")
print("5. GDP季度对比柱状图.png - 比较不同年份的季度GDP")
print("6. GDP产业结构雷达图.png - 对比不同年份的产业结构")
print("7. 地区季度销售对比柱状图.png - 比较各地区季度销售表现")
print("8. 地区销售占比饼图.png - 展示各地区销售额占比")
print("9. 地区销售分布箱形图.png - 分析各地区销售额分布特征")
print(f"\n所有图表已保存到 'charts_ultimate' 文件夹中")
print(f"使用字体: {font_name if font_name else '系统默认字体'}")

# 创建最终测试图
print("\n正在生成最终中文字体测试图...")
plt.figure(figsize=(12, 8))
test_data = ['北京市销售额', '上海市销售额', '广州市销售额', '深圳市销售额', '杭州市销售额']
test_values = [1193, 1051, 890, 950, 820]
bars = plt.bar(test_data, test_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'])

# 手动设置所有文字的字体
if chinese_font:
    plt.title('终极中文字体显示测试 - 各城市销售数据对比', fontproperties=chinese_font, fontsize=16, fontweight='bold')
    plt.xlabel('城市销售数据', fontproperties=chinese_font, fontsize=12)
    plt.ylabel('销售额（万元）', fontproperties=chinese_font, fontsize=12)

    # 设置x轴标签
    ax = plt.gca()
    ax.set_xticklabels(test_data, fontproperties=chinese_font, rotation=45)
else:
    plt.title('终极中文字体显示测试 - 各城市销售数据对比', fontsize=16, fontweight='bold')
    plt.xlabel('城市销售数据', fontsize=12)
    plt.ylabel('销售额（万元）', fontsize=12)
    plt.xticks(rotation=45)

# 添加数值标签
for bar, value in zip(bars, test_values):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20,
            f'{value}万', ha='center', va='bottom', fontsize=10,
            fontproperties=chinese_font if chinese_font else None)

plt.tight_layout()
plt.savefig('charts_ultimate/终极中文字体测试.png', dpi=300, bbox_inches='tight')
plt.show()

print("✓ 终极中文字体测试完成")
print("\n🎉 如果您现在还看到方框，请:")
print("1. 检查 charts_ultimate 文件夹中的图片文件")
print("2. 确认系统已安装中文字体")
print("3. 重启Python环境后重新运行")
print("4. 如果问题仍然存在，可能需要手动安装字体文件")
