import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据集路径
datasets = {
    '产品销售统计表': '数据可视化数据集-A/产品销售统计表.xlsx',
    '国内生产总值季度数据': '数据可视化数据集-A/国内生产总值季度数据.xlsx',
    '某公司产品销售数据': '数据可视化数据集-A/某公司产品销售数据.xlsx',
    '营销和产品销售表': '数据可视化数据集-A/营销和产品销售表.xlsx'
}

# 探索每个数据集
for name, path in datasets.items():
    print(f"\n{'='*50}")
    print(f"数据集: {name}")
    print(f"{'='*50}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(path)
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据:")
        print(df.head())
        print("\n数据类型:")
        print(df.dtypes)
        print("\n基本统计信息:")
        print(df.describe())
        
        # 检查缺失值
        missing_values = df.isnull().sum()
        if missing_values.sum() > 0:
            print("\n缺失值:")
            print(missing_values[missing_values > 0])
        else:
            print("\n无缺失值")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")
