"""
深度诊断中文字体问题
"""
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import matplotlib
import platform
import os

print("=" * 60)
print("深度诊断中文字体显示问题")
print("=" * 60)

# 1. 检查matplotlib版本和配置
print(f"1. 系统信息:")
print(f"   操作系统: {platform.system()} {platform.release()}")
print(f"   Python版本: {platform.python_version()}")
print(f"   Matplotlib版本: {matplotlib.__version__}")
print(f"   Matplotlib配置目录: {matplotlib.get_configdir()}")
print(f"   Matplotlib缓存目录: {matplotlib.get_cachedir()}")

# 2. 检查当前字体设置
print(f"\n2. 当前matplotlib字体配置:")
print(f"   font.family: {plt.rcParams['font.family']}")
print(f"   font.sans-serif: {plt.rcParams['font.sans-serif']}")
print(f"   axes.unicode_minus: {plt.rcParams['axes.unicode_minus']}")

# 3. 列出所有可用字体
print(f"\n3. 系统字体检查:")
all_fonts = [f.name for f in fm.fontManager.ttflist]
print(f"   系统总字体数: {len(all_fonts)}")

# 查找中文字体
chinese_fonts = []
chinese_keywords = ['YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 'Microsoft', 'Hei', 'Song']
for font in all_fonts:
    for keyword in chinese_keywords:
        if keyword.lower() in font.lower():
            chinese_fonts.append(font)
            break

print(f"   找到可能的中文字体 ({len(chinese_fonts)}个):")
for font in sorted(set(chinese_fonts))[:10]:  # 显示前10个
    print(f"     - {font}")

# 4. 强制清除缓存并重建
print(f"\n4. 清除matplotlib缓存...")
try:
    # 删除缓存文件
    cache_dir = matplotlib.get_cachedir()
    if os.path.exists(cache_dir):
        import shutil
        shutil.rmtree(cache_dir, ignore_errors=True)
        print("   ✓ 缓存目录已清除")
    
    # 重建字体缓存
    fm._rebuild()
    print("   ✓ 字体缓存已重建")
except Exception as e:
    print(f"   ⚠ 缓存清除失败: {e}")

# 5. 尝试多种字体配置方法
print(f"\n5. 测试不同字体配置方法:")

# 方法1: 直接指定字体文件路径
def method1_font_path():
    """方法1: 使用字体文件路径"""
    try:
        # Windows系统字体路径
        font_paths = [
            r'C:\Windows\Fonts\msyh.ttc',  # 微软雅黑
            r'C:\Windows\Fonts\simhei.ttf',  # 黑体
            r'C:\Windows\Fonts\simsun.ttc',  # 宋体
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                from matplotlib.font_manager import FontProperties
                font_prop = FontProperties(fname=font_path)
                print(f"   ✓ 找到字体文件: {font_path}")
                return font_prop
        return None
    except Exception as e:
        print(f"   ✗ 方法1失败: {e}")
        return None

# 方法2: 强制设置字体
def method2_force_font():
    """方法2: 强制设置字体"""
    try:
        plt.rcParams.clear()  # 清除所有设置
        plt.rcParams.update({
            'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'DejaVu Sans'],
            'axes.unicode_minus': False,
            'font.family': 'sans-serif',
            'font.size': 12
        })
        print("   ✓ 方法2: 强制字体设置完成")
        return True
    except Exception as e:
        print(f"   ✗ 方法2失败: {e}")
        return False

# 方法3: 使用fontconfig
def method3_fontconfig():
    """方法3: 使用系统fontconfig"""
    try:
        import subprocess
        result = subprocess.run(['fc-list', ':lang=zh'], capture_output=True, text=True)
        if result.returncode == 0:
            fonts = result.stdout.strip().split('\n')
            print(f"   ✓ 方法3: 找到{len(fonts)}个中文字体")
            return True
        else:
            print("   ✗ 方法3: fontconfig不可用")
            return False
    except Exception as e:
        print(f"   ✗ 方法3失败: {e}")
        return False

# 执行测试
font_prop = method1_font_path()
method2_force_font()
method3_fontconfig()

# 6. 创建测试图表
print(f"\n6. 创建测试图表...")

# 测试1: 使用字体文件路径
if font_prop:
    try:
        plt.figure(figsize=(10, 6))
        plt.plot([1, 2, 3], [1, 4, 2], 'o-')
        plt.title('中文字体测试 - 方法1（字体文件路径）', fontproperties=font_prop, fontsize=16)
        plt.xlabel('横轴标签', fontproperties=font_prop, fontsize=12)
        plt.ylabel('纵轴标签', fontproperties=font_prop, fontsize=12)
        plt.savefig('font_test_method1.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("   ✓ 方法1测试图表已保存: font_test_method1.png")
    except Exception as e:
        print(f"   ✗ 方法1测试失败: {e}")

# 测试2: 使用rcParams设置
try:
    plt.figure(figsize=(10, 6))
    plt.plot([1, 2, 3], [1, 4, 2], 's-', color='red')
    plt.title('中文字体测试 - 方法2（rcParams设置）', fontsize=16)
    plt.xlabel('横轴标签', fontsize=12)
    plt.ylabel('纵轴标签', fontsize=12)
    plt.savefig('font_test_method2.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("   ✓ 方法2测试图表已保存: font_test_method2.png")
except Exception as e:
    print(f"   ✗ 方法2测试失败: {e}")

# 测试3: 使用Unicode编码
try:
    plt.figure(figsize=(10, 6))
    plt.plot([1, 2, 3], [1, 4, 2], '^-', color='green')
    # 使用Unicode编码
    plt.title('\u4e2d\u6587\u5b57\u4f53\u6d4b\u8bd5 - \u65b9\u6cd53\uff08Unicode\u7f16\u7801\uff09', fontsize=16)
    plt.xlabel('\u6a2a\u8f74\u6807\u7b7e', fontsize=12)
    plt.ylabel('\u7eb5\u8f74\u6807\u7b7e', fontsize=12)
    plt.savefig('font_test_method3.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("   ✓ 方法3测试图表已保存: font_test_method3.png")
except Exception as e:
    print(f"   ✗ 方法3测试失败: {e}")

print(f"\n7. 诊断完成！")
print(f"请检查生成的测试图片:")
print(f"   - font_test_method1.png (字体文件路径方法)")
print(f"   - font_test_method2.png (rcParams设置方法)")
print(f"   - font_test_method3.png (Unicode编码方法)")
print(f"\n如果所有方法都失败，可能需要:")
print(f"   1. 重新安装matplotlib: pip install --upgrade matplotlib")
print(f"   2. 安装中文字体包")
print(f"   3. 重启Python环境")
