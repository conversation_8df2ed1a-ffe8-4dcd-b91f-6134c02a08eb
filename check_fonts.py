import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

print("检查系统字体和中文显示问题...")
print("="*50)

# 检查操作系统
print(f"操作系统: {platform.system()}")

# 获取所有可用字体
fonts = [f.name for f in fm.fontManager.ttflist]
print(f"\n系统中共有 {len(fonts)} 个字体")

# 查找中文字体
chinese_fonts = []
common_chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 
                       'Microsoft JhengHei', 'PingFang SC', 'Heiti SC', 'STHeiti',
                       'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans SC']

for font in common_chinese_fonts:
    if font in fonts:
        chinese_fonts.append(font)

print(f"\n找到的中文字体:")
if chinese_fonts:
    for i, font in enumerate(chinese_fonts, 1):
        print(f"  {i}. {font}")
else:
    print("  未找到常见中文字体")

# 显示当前matplotlib配置
print(f"\n当前matplotlib字体配置:")
print(f"  font.family: {plt.rcParams['font.family']}")
print(f"  font.sans-serif: {plt.rcParams['font.sans-serif']}")

# 测试中文显示
print(f"\n测试中文字体显示...")

# 尝试不同的字体设置方法
test_methods = [
    # 方法1: 直接设置SimHei
    {'font.sans-serif': ['SimHei'], 'axes.unicode_minus': False},
    # 方法2: 设置多个备选字体
    {'font.sans-serif': ['Microsoft YaHei', 'SimHei', 'DejaVu Sans'], 'axes.unicode_minus': False},
    # 方法3: 使用系统默认字体
    {'font.sans-serif': ['Arial Unicode MS', 'Microsoft YaHei', 'SimHei'], 'axes.unicode_minus': False},
]

for i, method in enumerate(test_methods, 1):
    try:
        plt.rcParams.update(method)
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.plot([1, 2, 3], [1, 4, 2], 'o-')
        ax.set_title('中文标题测试 - 数据可视化')
        ax.set_xlabel('横轴标签（中文）')
        ax.set_ylabel('纵轴标签（中文）')
        
        # 保存测试图片
        plt.savefig(f'font_test_{i}.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"  方法{i}: 成功生成测试图片 font_test_{i}.png")
        
    except Exception as e:
        print(f"  方法{i}: 失败 - {e}")

# 推荐解决方案
print(f"\n推荐解决方案:")
if platform.system() == 'Windows':
    print("Windows系统推荐使用:")
    print("  1. Microsoft YaHei (微软雅黑)")
    print("  2. SimHei (黑体)")
    print("  3. SimSun (宋体)")
elif platform.system() == 'Darwin':  # macOS
    print("macOS系统推荐使用:")
    print("  1. PingFang SC")
    print("  2. Heiti SC")
    print("  3. STHeiti")
else:  # Linux
    print("Linux系统推荐使用:")
    print("  1. WenQuanYi Micro Hei")
    print("  2. Noto Sans CJK SC")
    print("  3. Source Han Sans SC")

print(f"\n如果仍有问题，请尝试:")
print("  1. 安装中文字体包")
print("  2. 清除matplotlib缓存: matplotlib.font_manager._rebuild()")
print("  3. 使用字体文件路径直接指定字体")
