import pandas as pd
import numpy as np

# 读取数据
marketing_df = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')
gdp_df = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')
sales_df = pd.read_excel('数据可视化数据集-A/某公司产品销售数据.xlsx')

print("数据可视化分析报告")
print("="*60)

# 营销数据分析
print("\n一、营销和产品销售数据分析")
print("-"*40)

# 计算相关性
marketing_corr = marketing_df['营销费用（元）'].corr(marketing_df['订单金额（元）'])
print(f"1. 营销趋势折线图分析:")
print(f"   - 营销费用与订单金额的相关系数: {marketing_corr:.3f}")
print(f"   - 平均营销费用: {marketing_df['营销费用（元）'].mean():.2f}元")
print(f"   - 平均订单金额: {marketing_df['订单金额（元）'].mean():.2f}元")
print(f"   - ROI (订单金额/营销费用): {(marketing_df['订单金额（元）'].sum() / marketing_df['营销费用（元）'].sum()):.2f}")

print(f"\n2. 营销相关性散点图分析:")
print(f"   - 营销费用与订单金额呈正相关关系 (r={marketing_corr:.3f})")
print(f"   - 展现量范围: {marketing_df['展现量'].min():,} - {marketing_df['展现量'].max():,}")
print(f"   - 点击量范围: {marketing_df['点击量'].min()} - {marketing_df['点击量'].max()}")

# 计算各指标相关性
correlation_cols = ['营销费用（元）', '展现量', '点击量', '订单金额（元）', 
                   '加购数', '下单新客数', '访问页面数', '进店数', '商品关注数']
corr_matrix = marketing_df[correlation_cols].corr()
strongest_corr = corr_matrix.abs().unstack().sort_values(ascending=False)
strongest_corr = strongest_corr[strongest_corr < 1.0].head(3)

print(f"\n3. 营销指标热力图分析:")
print(f"   - 最强相关性指标对:")
for i, (pair, corr_val) in enumerate(strongest_corr.items(), 1):
    print(f"     {i}. {pair[0]} vs {pair[1]}: {corr_val:.3f}")

# GDP数据分析
print(f"\n二、国内生产总值季度数据分析")
print("-"*40)

# 计算产业占比
latest_quarter = '2022年第四季度'
total_gdp = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）'][latest_quarter].values[0]
primary_gdp = gdp_df[gdp_df['指标'] == '第一产业增加值（亿元）'][latest_quarter].values[0]
secondary_gdp = gdp_df[gdp_df['指标'] == '第二产业增加值（亿元）'][latest_quarter].values[0]
tertiary_gdp = gdp_df[gdp_df['指标'] == '第三产业增加值（亿元）'][latest_quarter].values[0]

print(f"4. GDP产业结构面积图分析:")
print(f"   - 2022年Q4三大产业占比:")
print(f"     第一产业: {(primary_gdp/total_gdp)*100:.1f}%")
print(f"     第二产业: {(secondary_gdp/total_gdp)*100:.1f}%")
print(f"     第三产业: {(tertiary_gdp/total_gdp)*100:.1f}%")

# 计算增长率
gdp_2019q4 = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）']['2019年第四季度'].values[0]
gdp_2022q4 = gdp_df[gdp_df['指标'] == '国内生产总值（亿元）']['2022年第四季度'].values[0]
growth_rate = ((gdp_2022q4 - gdp_2019q4) / gdp_2019q4) * 100

print(f"\n5. GDP季度对比柱状图分析:")
print(f"   - 2019Q4-2022Q4总体增长率: {growth_rate:.1f}%")
print(f"   - 2022Q4 GDP总量: {gdp_2022q4:,.1f}亿元")
print(f"   - 2019Q4 GDP总量: {gdp_2019q4:,.1f}亿元")

print(f"\n6. GDP产业结构雷达图分析:")
print(f"   - 第三产业始终占主导地位，是经济增长的主要动力")
print(f"   - 第二产业保持稳定，制造业基础扎实")
print(f"   - 第一产业占比相对较小但保持稳定")

# 销售数据分析
print(f"\n三、某公司产品销售数据分析")
print("-"*40)

# 地区销售分析
region_total = sales_df.groupby('地区')['销售额（万元）'].sum()
region_avg = sales_df.groupby('地区')['销售额（万元）'].mean()
total_sales = region_total.sum()

print(f"7. 地区季度销售对比柱状图分析:")
for region in region_total.index:
    region_data = sales_df[sales_df['地区'] == region]['销售额（万元）']
    print(f"   - {region}: 总销售额{region_total[region]}万元, 平均{region_avg[region]:.1f}万元/季度")

print(f"\n8. 地区销售占比饼图分析:")
for region in region_total.index:
    percentage = (region_total[region] / total_sales) * 100
    print(f"   - {region}: {percentage:.1f}%")

print(f"\n9. 地区销售分布箱形图分析:")
for region in sales_df['地区'].unique():
    region_data = sales_df[sales_df['地区'] == region]['销售额（万元）']
    print(f"   - {region}: 中位数{region_data.median():.0f}万元, 标准差{region_data.std():.1f}万元")

print(f"\n总结:")
print(f"- 营销投入与收益呈正相关，ROI为{(marketing_df['订单金额（元）'].sum() / marketing_df['营销费用（元）'].sum()):.2f}")
print(f"- 中国经济以第三产业为主导，占GDP的{(tertiary_gdp/total_gdp)*100:.1f}%")
print(f"- 北京市销售表现最佳，占总销售额的{(region_total['北京市']/total_sales)*100:.1f}%")
